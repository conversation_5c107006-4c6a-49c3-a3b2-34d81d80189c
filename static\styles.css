/* Modern styles for Dinnerrr */

:root {
  --primary-color: #ff6b6b;
  --secondary-color: #4ecdc4;
  --dark-color: #2d3436;
  --light-color: #f8f9fa;
  --accent-color: #ffd166;
  --text-color: #2c3e50;
  --border-radius: 10px;
  --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body {
  font-family: 'Poppins', sans-serif;
  color: var(--text-color);
  background-color: var(--light-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Navbar styling */
.navbar {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.navbar-brand {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: var(--dark-color);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: color 0.3s ease;
    opacity: 1;
    visibility: visible;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.75rem;
}

.navbar-toggler:focus {
    box-shadow: none;
    outline: none;
}

/* Ensure dropdown menus are visible */
.dropdown-menu {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), 
              url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80');
  background-size: cover;
  background-position: center;
  padding: 100px 0;
  margin-bottom: 30px;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.search-card {
  background-color: white;
  padding: 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-top: 20px;
}

/* Section Titles */
.section-title {
  position: relative;
  margin-bottom: 30px;
  padding-bottom: 15px;
  font-weight: 700;
  color: var(--dark-color);
}

.section-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

/* Restaurant Cards */
.restaurant-card {
  border: none;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.restaurant-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.card-img-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-img-top {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.restaurant-card:hover .card-img-top {
  transform: scale(1.05);
}

.placeholder-img {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  color: #999;
}

.wishlist-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.wishlist-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Rating and Price */
.rating {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.rating-score {
  font-weight: bold;
  margin-right: 5px;
}

.stars {
  color: var(--accent-color);
  margin-right: 5px;
}

.review-count {
  color: #777;
  font-size: 0.9em;
}

.price-tag {
  display: inline-block;
  padding: 3px 8px;
  background-color: #e9ecef;
  border-radius: 20px;
  font-weight: 500;
  margin-left: 10px;
}

.location {
  color: #666;
  font-size: 0.9em;
  margin-bottom: 15px;
}

/* Cuisine Grid */
.cuisine-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.cuisine-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 20px;
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: transform 0.3s ease;
  text-decoration: none;
  color: var(--text-color);
}

.cuisine-card:hover {
  transform: translateY(-5px);
  color: var(--primary-color);
}

.cuisine-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: var(--primary-color);
}

/* Footer */
.footer {
  background-color: var(--dark-color);
  color: white;
  padding: 50px 0 20px;
  margin-top: auto;
}

.footer h5 {
  color: var(--primary-color);
  margin-bottom: 20px;
  font-weight: 600;
}

.footer a {
  color: #ddd;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer a:hover {
  color: var(--primary-color);
}

.social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  margin-right: 10px;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0;
  }
  
  .search-card {
    padding: 20px;
  }
}
