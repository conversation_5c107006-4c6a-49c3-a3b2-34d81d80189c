{% extends "base.html" %}

{% block title %}Explore Restaurants{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="display-5 fw-bold mb-4">Explore Restaurants</h1>
    
    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title mb-3">Filter Options</h5>
            <form id="explore-filters" class="row g-3">
                <div class="col-md-4">
                    <label for="cuisine" class="form-label">Cuisine</label>
                    <select class="form-select" id="cuisine" name="cuisine">
                        <option value="">All Cuisines</option>
                        <option value="italian">Italian</option>
                        <option value="mexican">Mexican</option>
                        <option value="chinese">Chinese</option>
                        <option value="japanese">Japanese</option>
                        <option value="indian">Indian</option>
                        <option value="thai">Thai</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="price" class="form-label">Price Range</label>
                    <select class="form-select" id="price" name="price">
                        <option value="">Any Price</option>
                        <option value="1">$</option>
                        <option value="2">$$</option>
                        <option value="3">$$$</option>
                        <option value="4">$$$$</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="rating" class="form-label">Minimum Rating</label>
                    <select class="form-select" id="rating" name="rating">
                        <option value="">Any Rating</option>
                        <option value="4">4+ Stars</option>
                        <option value="3">3+ Stars</option>
                        <option value="2">2+ Stars</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">Apply Filters</button>
                    <button type="reset" class="btn btn-outline-secondary">Reset</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Results Section -->
    <div id="explore-results">
        <div class="row row-cols-1 row-cols-md-3 g-4" id="restaurants-container">
            <!-- Results will be loaded here -->
            <div class="col-12 text-center py-5">
                <p class="text-muted">Use the filters above to discover restaurants</p>
            </div>
        </div>
    </div>
    
    <!-- Pagination -->
    <nav aria-label="Restaurant results pagination" class="mt-4">
        <ul class="pagination justify-content-center" id="pagination-container">
            <!-- Pagination will be added dynamically -->
        </ul>
    </nav>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('explore-filters');
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(form);
            const params = new URLSearchParams(formData);
            
            // Show loading state
            document.getElementById('restaurants-container').innerHTML = `
                <div class="col-12 text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `;
            
            // Fetch results
            fetch(`/api/explore?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    displayResults(data);
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('restaurants-container').innerHTML = `
                        <div class="col-12 text-center py-4">
                            <div class="alert alert-danger">Error loading results. Please try again.</div>
                        </div>
                    `;
                });
        });
        
        function displayResults(data) {
            const container = document.getElementById('restaurants-container');
            
            if (!data.businesses || data.businesses.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center py-4">
                        <div class="alert alert-info">No restaurants found matching your criteria.</div>
                    </div>
                `;
                return;
            }
            
            let html = '';
            data.businesses.forEach(restaurant => {
                html += `
                    <div class="col">
                        <div class="card restaurant-card h-100">
                            <div class="card-img-container">
                                ${restaurant.image_url ? 
                                    `<img src="${restaurant.image_url}" class="card-img-top" alt="${restaurant.name}">` : 
                                    `<div class="placeholder-img"><i class="fas fa-utensils fa-3x"></i></div>`
                                }
                            </div>
                            <div class="card-body">
                                <h5 class="card-title">${restaurant.name}</h5>
                                <div class="mb-2">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i>${restaurant.rating}
                                    </span>
                                    <span class="text-muted">(${restaurant.review_count} reviews)</span>
                                </div>
                                <p class="card-text">
                                    <small class="text-muted">
                                        <i class="fas fa-map-marker-alt me-1"></i>${restaurant.location.address1}
                                    </small>
                                </p>
                                <a href="{{ url_for('restaurant_detail', business_id=restaurant.id) }}" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-info-circle me-1"></i>View Details
                                </a>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // Update pagination
            updatePagination(data.total, data.offset, data.limit);
        }
        
        function updatePagination(total, offset, limit) {
            // Implementation for pagination
        }
    });
</script>
{% endblock %}
