{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Create New Account</h3>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }}">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <form method="POST" action="{{ url_for('register') }}" class="needs-validation" novalidate>
                        <div class="form-group mb-3">
                            <label for="name">Full Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">Please enter your name.</div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="email">Email address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="invalid-feedback">Please enter a valid email address.</div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="password">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            <small class="form-text text-muted">Password must be at least 6 characters long</small>
                            <div class="invalid-feedback">Password must be at least 6 characters long.</div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="confirm_password">Confirm Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                            <div class="invalid-feedback">Passwords do not match.</div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Register</button>
                            <a href="{{ url_for('login') }}" class="btn btn-outline-secondary">Already have an account? Login</a>
                        </div>
                    </form>

                    <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        const form = document.querySelector('form');
                        const password = document.getElementById('password');
                        const confirmPassword = document.getElementById('confirm_password');
                        
                        // Validate password match
                        confirmPassword.addEventListener('input', function() {
                            if (password.value !== confirmPassword.value) {
                                confirmPassword.setCustomValidity("Passwords don't match");
                            } else {
                                confirmPassword.setCustomValidity('');
                            }
                        });
                        
                        password.addEventListener('input', function() {
                            if (password.value !== confirmPassword.value) {
                                confirmPassword.setCustomValidity("Passwords don't match");
                            } else {
                                confirmPassword.setCustomValidity('');
                            }
                        });
                        
                        // Form validation
                        form.addEventListener('submit', function(event) {
                            if (!form.checkValidity()) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        });
                    });
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 



