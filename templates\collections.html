{% extends "base.html" %}

{% block title %}Restaurant Collections{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="display-5 fw-bold mb-4">Restaurant Collections</h1>
    
    <div class="row row-cols-1 row-cols-md-2 g-4 mb-5">
        <div class="col">
            <div class="card collection-card h-100">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-award me-2"></i>Top Rated</h5>
                    <p class="card-text">Discover the highest-rated restaurants in your area.</p>
                    <a href="{{ url_for('explore') }}?rating=4.5" class="btn btn-outline-primary">Explore</a>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card collection-card h-100">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-fire me-2"></i>Trending Now</h5>
                    <p class="card-text">See what's popular and trending this week.</p>
                    <a href="{{ url_for('explore') }}?sort_by=trending" class="btn btn-outline-primary">Explore</a>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card collection-card h-100">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-utensils me-2"></i>Fine Dining</h5>
                    <p class="card-text">Elegant restaurants perfect for special occasions.</p>
                    <a href="{{ url_for('explore') }}?price=4&categories=fine_dining" class="btn btn-outline-primary">Explore</a>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card collection-card h-100">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-coffee me-2"></i>Cozy Cafés</h5>
                    <p class="card-text">Perfect spots for coffee, pastries, and relaxation.</p>
                    <a href="{{ url_for('explore') }}?categories=cafes,coffee" class="btn btn-outline-primary">Explore</a>
                </div>
            </div>
        </div>
    </div>
    
    <h2 class="section-title mb-4">Cuisine Collections</h2>
    <div class="row row-cols-1 row-cols-md-3 row-cols-lg-4 g-4">
        <div class="col">
            <div class="cuisine-collection-card">
                <img src="https://images.unsplash.com/photo-1555939594-58d7cb561ad1" alt="Italian" class="img-fluid rounded">
                <div class="cuisine-overlay">
                    <h3>Italian</h3>
                    <a href="{{ url_for('explore') }}?cuisine=italian" class="btn btn-light btn-sm">View All</a>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="cuisine-collection-card">
                <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ae38" alt="Japanese" class="img-fluid rounded">
                <div class="cuisine-overlay">
                    <h3>Japanese</h3>
                    <a href="{{ url_for('explore') }}?cuisine=japanese" class="btn btn-light btn-sm">View All</a>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="cuisine-collection-card">
                <img src="https://images.unsplash.com/photo-1582169296194-e4d644c48063" alt="Mexican" class="img-fluid rounded">
                <div class="cuisine-overlay">
                    <h3>Mexican</h3>
                    <a href="{{ url_for('explore') }}?cuisine=mexican" class="btn btn-light btn-sm">View All</a>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="cuisine-collection-card">
                <img src="https://images.unsplash.com/photo-1585937421612-70a008356cf4" alt="Thai" class="img-fluid rounded">
                <div class="cuisine-overlay">
                    <h3>Thai</h3>
                    <a href="{{ url_for('explore') }}?cuisine=thai" class="btn btn-light btn-sm">View All</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .collection-card {
        transition: transform 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .collection-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .cuisine-collection-card {
        position: relative;
        border-radius: 10px;
        overflow: hidden;
        height: 200px;
        margin-bottom: 20px;
    }
    
    .cuisine-collection-card img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .cuisine-collection-card:hover img {
        transform: scale(1.1);
    }
    
    .cuisine-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
        padding: 20px;
        color: white;
    }
    
    .cuisine-overlay h3 {
        margin-bottom: 10px;
        font-size: 1.2rem;
    }
</style>
{% endblock %}